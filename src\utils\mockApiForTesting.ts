// Temporary mock API for testing role update functionality
// This file can be used when the backend is not available

import { UpdateUserRolesDto, RoleUpdateValidationDto } from '../apis/userManagement';

// DEPRECATED: Mock functions replaced with real API calls
// These functions are no longer used as the application now uses real API endpoints:
// - PUT /api/usermanagement/{userId}/roles - Update user roles
// - POST /api/usermanagement/{userId}/roles/validate - Validate role changes

export const mockValidateRoleUpdate = async (
  _userId: string,
  _roleData: UpdateUserRolesDto
): Promise<RoleUpdateValidationDto> => {
  console.warn('mockValidateRoleUpdate is deprecated. Use userManagementApi.validateRoleUpdate instead.');
  throw new Error('Mock API functions have been replaced with real API calls. Please use userManagementApi.validateRoleUpdate.');
};

export const mockUpdateUserRoles = async (
  _userId: string,
  _roleData: UpdateUserRolesDto
): Promise<void> => {
  console.warn('mockUpdateUserRoles is deprecated. Use userManagementApi.updateUserRoles instead.');
  throw new Error('Mock API functions have been replaced with real API calls. Please use userManagementApi.updateUserRoles.');
};

// DEPRECATED: Mock mode is no longer needed as real API endpoints are now implemented
export const enableMockMode = () => {
  console.warn('� enableMockMode is deprecated. The application now uses real API endpoints.');
  console.log('Real API endpoints implemented:');
  console.log('- PUT /api/usermanagement/{userId}/roles - Update user roles');
  console.log('- POST /api/usermanagement/{userId}/roles/validate - Validate role changes');

  // No longer override fetch - let real API calls go through
  return;
};

// DEPRECATED: Mock mode is no longer needed
export const disableMockMode = () => {
  console.warn('disableMockMode is deprecated. Mock mode is no longer used.');
  return;
};

// DEPRECATED: Auto-enable mock mode is no longer needed
// Real API endpoints are now implemented and used directly
if (import.meta.env.VITE_ENABLE_MOCK_API === 'true') {
  console.warn('VITE_ENABLE_MOCK_API environment variable is deprecated. Real API endpoints are now used.');
}
