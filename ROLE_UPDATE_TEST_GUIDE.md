# Role Update Test Guide

## 🔧 Testing the 400 Bad Request Fix

### Before Testing
1. Open browser developer tools (F12)
2. Go to Network tab to monitor API requests
3. Go to Console tab to see any error messages

### Test Case 1: Regular Role Change (User → CJ)
**Steps:**
1. Navigate to User Management page
2. Find a user with "User" role
3. Click "Edit" button
4. Change role dropdown from "User" to "CJ"
5. Click "Save Changes"

**Expected Results:**
- ✅ No 400 Bad Request error
- ✅ Success toast: "User role updated successfully"
- ✅ Modal closes automatically
- ✅ User list refreshes with new role
- ✅ Network tab shows successful PUT request

**Check Network Request:**
- URL: `PUT /api/UserManagement/{userId}`
- Request Body should contain: `"roleNames": ["CJ"]` (not `["cj"]`)

### Test Case 2: Admin Role Assignment (With Confirmation)
**Steps:**
1. Navigate to User Management page
2. Find a user with "User" or "CJ" role
3. Click "Edit" button
4. Change role dropdown to "Admin"
5. Click "Save Changes"
6. Confirmation dialog should appear
7. Click "Confirm Changes"

**Expected Results:**
- ✅ No 400 Bad Request error
- ✅ Confirmation dialog shows warnings about admin privileges
- ✅ After confirmation: Success toast appears
- ✅ Modal closes automatically
- ✅ User list refreshes with "Admin" role
- ✅ Network tab shows successful PUT request

**Check Network Request:**
- URL: `PUT /api/UserManagement/{userId}`
- Request Body should contain: `"roleNames": ["Admin"]` (not `["admin"]`)

### Test Case 3: Combined Update (Role + Other Fields)
**Steps:**
1. Navigate to User Management page
2. Click "Edit" on any user
3. Change the name AND the role
4. Click "Save Changes"

**Expected Results:**
- ✅ No 400 Bad Request error
- ✅ Both name and role are updated
- ✅ Success toast appears
- ✅ User list shows both changes

**Check Network Request:**
- Request Body should contain both updated fields with proper role format

### Test Case 4: Error Handling
**Steps:**
1. Disconnect from internet or stop backend server
2. Try to update a user role
3. Reconnect/restart backend and try again

**Expected Results:**
- ✅ Network error handled gracefully
- ✅ Error toast appears for network issues
- ✅ After reconnection, updates work normally

## 🔍 Debugging Information

### Console Logs to Look For
```
✅ GOOD: No "Failed to update user: AxiosError" messages
✅ GOOD: "User role updated successfully" toast appears
✅ GOOD: Network requests return 200 status
```

### Network Request Format
**Correct Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>", 
  "phone": "+1234567890",
  "roleNames": ["Admin"]  // ← Proper case!
}
```

**Incorrect Request Body (Fixed):**
```json
{
  "roleNames": ["admin"]  // ← This caused 400 errors
}
```

### Available Roles Format
The API returns roles in proper case:
- `"Admin"` (not `"admin"`)
- `"User"` (not `"user"`)
- `"CJ"` (not `"cj"`)

## 🎯 Success Criteria

### ✅ All Tests Pass When:
1. **No 400 Bad Request errors** in console or network tab
2. **Role updates complete successfully** with proper API responses
3. **UI refreshes immediately** after successful updates
4. **Confirmation dialogs work** for admin role assignments
5. **Success notifications appear** for all successful updates
6. **Error handling works** for network issues

### 🚨 If Tests Still Fail:
1. Check browser console for new error messages
2. Verify network requests show proper role name format
3. Confirm backend is running and accessible
4. Check if backend API expects different field names

## 📋 Quick Verification Checklist

- [ ] User → CJ role change works without errors
- [ ] User → Admin role change shows confirmation dialog
- [ ] Admin role confirmation completes successfully
- [ ] Combined name + role updates work
- [ ] Success toasts appear for all updates
- [ ] User list refreshes after updates
- [ ] No 400 Bad Request errors in console
- [ ] Network requests show proper role format

## 🔧 Technical Details

### Root Cause of 400 Error
The backend expected role names in proper case format (`"Admin"`, `"User"`, `"CJ"`) but the frontend was sending lowercase versions (`"admin"`, `"user"`, `"cj"`).

### Fix Implementation
1. **Role Selection**: Convert dropdown values to proper case
2. **Role Initialization**: Map existing user roles to proper case
3. **Role Validation**: Use case-insensitive validation logic
4. **API Requests**: Send properly formatted role names

The fix ensures that regardless of how roles are stored or displayed in the UI, the API always receives the correct format expected by the backend.
